@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use 'common/popup' as *;

@include b(dialog) {
  @include set-component-css-var('dialog', $dialog);

  position: relative;
  margin: var(#{getCssVarName('dialog-margin-top')}, 15vh) auto 50px;
  background: getCssVar('dialog', 'bg-color');
  border-radius: getCssVar('dialog', 'border-radius');
  box-shadow: getCssVar('dialog', 'box-shadow');
  box-sizing: border-box;
  // padding: 0px getCssVar('dialog', 'padding-primary');
  width: var(#{getCssVarName('dialog-width')}, 50%);
  overflow-wrap: break-word;

  &:focus {
    outline: none !important;
  }

  @include when(align-center) {
    margin: auto;
  }

  @include when(fullscreen) {
    @include set-css-var-value('dialog-width', 100%);
    @include set-css-var-value('dialog-margin-top', 0);

    margin-bottom: 0;
    height: 100%;
    overflow: auto;
    border-radius: 0px;
  }

  @include e(wrapper) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
  }

  @include when(draggable) {
    @include e(header) {
      cursor: move;
      user-select: none;
    }
  }

  @include e(header) {
    padding: 18px 14px;
    display: flex;
    align-items: center;
    position: relative;
    min-width: 0; // 允许 flex 容器收缩

    &.show-close {
      padding-right: calc(getCssVar('dialog', 'padding-primary') + 48px);
    }

    // 全宽分割线
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: getCssVar('border-color-lighter');
    }

    // 添加橙色圆圈图标
    &::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 3px solid #ff9c00;
      background: #fff;
      border-radius: 50%;
      margin-right: 6px;
      flex-shrink: 0;
    }
  }

  @include e(headerbtn) {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    padding-top: 7px;
    width: 48px;
    height: 48px;
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;
    font-size: var(#{getCssVarName('message-close-size')}, map.get($message, 'close-size'));

    .#{$namespace}-dialog__close {
      color: getCssVar('color', 'info');
      font-size: inherit;
    }

    &:focus,
    &:hover {
      .#{$namespace}-dialog__close {
        color: getCssVar('color', 'primary');
      }
    }
  }

  @include e(title) {
    line-height: 1;
    font-size: 15px;
    color: getCssVar('text-color', 'primary');
    font-weight: 500;
    margin: 0;
    flex: 1;
    min-width: 0; // 允许 flex 项目收缩到内容宽度以下
    display: flex;
    align-items: center;

    // 标题容器样式
    > div {
      display: flex;
      align-items: center;
      flex: 0 1 auto;
      min-width: 0;
      max-width: 100%; // 确保不超出容器
    }

    // 对标题文字 span 应用省略号处理
    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
  }

  @include e(title-tip) {
    color: getCssVar('color', 'info');
    font-size: 16px;

    &:hover {
      color: getCssVar('color', 'primary');
    }
  }

  @include e(body) {
    color: getCssVar('text-color', 'primary');
    font-size: getCssVar('dialog-content-font-size');
    padding: 20px 34px;
    position: relative;
  }

  @include e(footer) {
    padding: 10px 16px;
    text-align: right;
    box-sizing: border-box;

    // 按钮间距
    .#{$namespace}-button + .#{$namespace}-button {
      margin-left: 8px;
    }
  }

  // 自定义 BsDialog 的 footer 按钮布局
  @include e(footer-buttons) {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  @include e(footer-left) {
    float: left;
  }

  @include m(has-left) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  // 内容居中布局
  @include m(center) {
    text-align: center;

    @include e(body) {
      text-align: initial;
    }

    @include e(footer) {
      text-align: inherit;
    }
  }

  // 尺寸变体公共样式（非全屏时）
  &#{&}--small:not(.is-fullscreen),
  &#{&}--default:not(.is-fullscreen),
  &#{&}--large:not(.is-fullscreen),
  &#{&}--xlarge:not(.is-fullscreen) {
    display: flex;
    flex-direction: column;

    .#{$namespace}-dialog__body {
      min-height: 320px;
      overflow-y: auto;
      flex: 1;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: getCssVar('fill-color', 'light');
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: getCssVar('border-color');
        border-radius: 3px;

        &:hover {
          background: getCssVar('border-color-dark');
        }
      }
    }

    // 只有当存在footer时才显示body底部分割线
    &:has(.#{$namespace}-dialog__footer) .#{$namespace}-dialog__body {
      border-bottom: 1px solid getCssVar('border-color-lighter');
    }
  }

  // 全屏时的样式
  &.is-fullscreen {
    display: flex;
    flex-direction: column;

    .#{$namespace}-dialog__body {
      flex: 1;
      overflow-y: auto;
    }
  }

  // 全屏时的样式
  &.is-fullscreen {
    .#{$namespace}-dialog__body {
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: getCssVar('fill-color', 'light');
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: getCssVar('border-color');
        border-radius: 3px;

        &:hover {
          background: getCssVar('border-color-dark');
        }
      }
    }
  }

  // 全屏模式下只有当存在footer时才显示body底部分割线
  &.is-fullscreen:has(.#{$namespace}-dialog__footer) .#{$namespace}-dialog__body {
    border-bottom: 1px solid getCssVar('border-color-lighter');
  }

  // 尺寸变体特定样式（非全屏时）
  @include m(small) {
    &:not(.is-fullscreen) {
      max-height: 550px;
    }
  }

  @include m(default) {
    &:not(.is-fullscreen) {
      max-height: 550px;
    }
  }

  @include m(large) {
    &:not(.is-fullscreen) {
      max-height: 550px;
    }
  }

  @include m(xlarge) {
    &:not(.is-fullscreen) {
      max-height: 600px;
    }
  }

  // 禁用动画样式
  @include when(animation-disabled) {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}

.#{$namespace}-overlay-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
}

.dialog-fade-enter-active {
  animation: modal-fade-in 3s;
  .#{$namespace}-overlay-dialog {
    animation: dialog-fade-in 3s;
  }

  // 禁用动画时
  &.is-animation-disabled {
    animation: none !important;
    .#{$namespace}-overlay-dialog {
      animation: none !important;
    }
  }
}

.dialog-fade-leave-active {
  animation: modal-fade-out 3s;
  .#{$namespace}-overlay-dialog {
    animation: dialog-fade-out 3s;
  }

  // 禁用动画时
  &.is-animation-disabled {
    animation: none !important;
    .#{$namespace}-overlay-dialog {
      animation: none !important;
    }
  }
}

@keyframes dialog-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes dialog-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}

@keyframes modal-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes modal-fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
