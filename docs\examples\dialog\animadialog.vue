<template>
  <div>
    <h2>测试禁用动画功能</h2>

    <div style="margin-bottom: 20px">
      <el-button @click="showNormalDialog = true">显示普通对话框（有动画）</el-button>
      <el-button @click="showDisabledDialog = true">显示禁用动画对话框</el-button>
    </div>

    <!-- 普通对话框 -->
    <BsDialog v-model="showNormalDialog" title="普通对话框" size="small" @ok="handleOk" @cancel="handleCancel">
      <p>这是一个普通的对话框，有弹出动画效果。</p>
    </BsDialog>

    <!-- 禁用动画的对话框 -->
    <BsDialog v-model="showDisabledDialog" title="禁用动画对话框" size="small" :disable-animation="true" @ok="handleOk" @cancel="handleCancel">
      <p>这是一个禁用了动画的对话框，应该没有弹出动画效果。</p>
    </BsDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElButton } from 'element-plus'
import { BsDialog } from '@bs-nexus/components/dialog'

const showNormalDialog = ref(false)
const showDisabledDialog = ref(false)

const handleOk = () => {
  console.log('确认按钮被点击')
}

const handleCancel = () => {
  console.log('取消按钮被点击')
}
</script>
