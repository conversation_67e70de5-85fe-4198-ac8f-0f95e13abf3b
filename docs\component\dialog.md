---
title: Dialog 对话框
lang: zh-CN
---

# Dialog 对话框

对话框组件用于在不离开当前页面的情况下与用户进行交互，提供信息展示、表单填写、确认操作等功能。

## 基础用法

通过 `v-model` 控制对话框的显示和隐藏。

:::demo 使用 `v-model` 与 Vue 实例中的一个变量进行双向绑定，控制对话框的显示和隐藏。

dialog/basic

:::

## 不同尺寸

Dialog 组件提供四种尺寸：小型、默认、大型和超大型。

:::demo 通过 `size` 属性设置对话框的尺寸，可选值为 `small`、`default`、`large` 和 `xlarge`。

dialog/basic-usage

:::

## 自定义内容

对话框的内容可以是任何内容，如表格、表单等。

:::demo 对话框的内容可以是任何内容，如表格、表单等。还可以使用 `footer-left` 插槽在底部左侧添加内容，使用 `header-class` 和 `footer-class` 自定义样式。

dialog/customization-content

:::

## 自定义标题

通过 `title` 插槽可以自定义对话框的标题，通过 `title-tip` 属性或插槽可以为标题添加提示信息。

:::demo 使用 `#title` 插槽可以自定义对话框的标题，可以添加图标、徽章等元素。使用 `title-tip` 属性可以为标题添加提示信息，使用 `#title-tip` 插槽可以自定义提示内容。

dialog/customization-header

:::

## 按钮配置

通过 `confirm-button` 和 `cancel-button` 属性可以自定义按钮的类型、文本、禁用状态和加载状态。

:::demo 使用 `confirm-button` 和 `cancel-button` 属性配置按钮的类型、文本、禁用状态和加载状态。

dialog/button-configuration

:::

## 事件处理

对话框提供了 `ok` 和 `cancel` 事件，分别在用户点击确认按钮和取消按钮时触发。

:::demo 使用 `@ok` 和 `@cancel` 监听确认和取消事件。当 `confirm-loading` 为 `true` 时，确认按钮会显示加载状态，并且 `ok` 事件会接收一个回调函数，用于关闭对话框。

dialog/events

:::

## 嵌套的对话框

如果需要在一个 Dialog 内部嵌套另一个 Dialog，需要使用 `append-to-body` 属性。

:::demo 通常我们不建议使用嵌套对话框。 如果你需要在页面上呈现多个对话框，你可以简单地打平它们，以便它们彼此之间是平级关系。 如果必须要在一个对话框内展示另一个对话框，可以将内部嵌套的对话框属性 `append-to-body` 设置为 true，嵌套的对话框将附加到 body 而不是其父节点，这样两个对话框都可以被正确地渲染。

dialog/nested-dialog

:::

## 关闭时销毁

启用此功能时，默认栏位下的内容将使用 `v-if` 指令销毁。 当出现性能问题时，可以启用此功能。

:::demo 需要注意的是，当这个属性被启用时，在 `transition.beforeEnter` 事件卸载前，除了 `overlay`、`header (可选)`与`footer(可选)` ，Dialog 内不会有其它任何其它的 DOM 节点存在。

dialog/destroy-on-close

:::

## 全屏

设置 `fullscreen` 属性来打开全屏对话框。

:::demo

dialog/animadialog

:::

:::tip

如果 `fullscreen` 为 true，则 `width`、`top` 和 `draggable` 属性无效。

:::

## API

### 属性

| 属性名                | 说明                                                         | 类型                                      | 默认值              |
| --------------------- | ------------------------------------------------------------ | ----------------------------------------- | ------------------- |
| model-value / v-model | 控制对话框的显示和隐藏                                       | boolean                                   | false               |
| title                 | 对话框的标题                                                 | string                                    | —                   |
| title-tip             | 标题提示信息                                                 | string                                    | —                   |
| size                  | 对话框的尺寸                                                 | 'small' / 'default' / 'large' / 'xlarge'  | 'small'             |
| width                 | 对话框的宽度                                                 | string / number                           | —                   |
| fullscreen            | 是否为全屏对话框                                             | boolean                                   | false               |
| top                   | 对话框 CSS 中的 margin-top 值                                | string                                    | —                   |
| modal                 | 是否需要遮罩层                                               | boolean                                   | true                |
| header-class          | 头部的自定义样式类                                           | string                                    | —                   |
| body-class            | 主体的自定义样式类                                           | string                                    | —                   |
| footer-class          | 底部的自定义样式类                                           | string                                    | —                   |
| append-to-body        | 对话框是否插入至 body 元素上                                 | boolean                                   | false               |
| append-to             | 对话框挂载到哪个 DOM 元素                                    | string                                    | 'body'              |
| close-on-click-modal  | 是否可以通过点击遮罩层关闭对话框                             | boolean                                   | false               |
| close-on-press-escape | 是否可以通过按下 ESC 关闭对话框                              | boolean                                   | false               |
| before-close          | 关闭前的回调函数                                             | function(done)，done 用于关闭对话框       | —                   |
| align-center          | 是否水平垂直对齐对话框                                       | boolean                                   | true                |
| destroy-on-close      | 关闭时销毁对话框中的元素                                     | boolean                                   | false               |
| footer-visible        | 是否显示底部区域                                             | boolean                                   | true                |
| confirm-button        | 确认按钮配置                                                 | [DialogButtonConfig](#dialogbuttonconfig) | { type: 'primary' } |
| cancel-button         | 取消按钮配置                                                 | [DialogButtonConfig](#dialogbuttonconfig) | { type: '' }        |
| confirm-loading       | 确认按钮是否为加载中状态，优先级高于确认按钮配置中的 loading | boolean                                   | false               |

### 事件

| 事件名             | 说明                           | 回调参数                                                                  |
| ------------------ | ------------------------------ | ------------------------------------------------------------------------- |
| ok                 | 点击确认按钮时触发             | 当 confirm-loading 为 true 时，回调函数接收一个 done 函数，用于关闭对话框 |
| cancel             | 点击取消按钮或关闭对话框时触发 | —                                                                         |
| update:model-value | 在对话框显示状态发生变化时触发 | 新的显示状态                                                              |

### 插槽

| 插槽名      | 说明                                     |
| ----------- | ---------------------------------------- |
| default     | 对话框的内容                             |
| title       | 对话框标题的内容，会覆盖 title 属性      |
| title-tip   | 标题提示气泡框的内容                     |
| footer      | 对话框按钮操作区的内容，会覆盖默认的按钮 |
| footer-left | 对话框底部左侧的内容                     |

### DialogButtonConfig

按钮配置对象，用于自定义确认按钮和取消按钮的属性。

| 属性名   | 说明     | 类型                                                                | 默认值 |
| -------- | -------- | ------------------------------------------------------------------- | ------ |
| type     | 按钮类型 | 'primary' / 'success' / 'warning' / 'danger' / 'info' / 'text' / '' | —      |
| text     | 按钮文本 | string                                                              | —      |
| disabled | 是否禁用 | boolean                                                             | false  |
