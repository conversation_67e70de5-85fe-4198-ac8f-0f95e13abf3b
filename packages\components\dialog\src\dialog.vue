<template>
  <Dialog
    v-model="dialogVisible"
    :title="title"
    :width="dialogWidth"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :header-class="headerClass"
    :body-class="bodyClass"
    :footer-class="footerClass"
    :append-to-body="appendToBody"
    :append-to="appendTo"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="true"
    :before-close="handleBeforeClose"
    :align-center="alignCenter"
    :destroy-on-close="destroyOnClose"
    :disable-animation="props.disableAnimation"
    :class="[dialogClass, props.disableAnimation ? DISABLE_ANIMATION_CLASSNAME : '']"
    @close="handleClose"
  >
    <!-- 标题插槽 -->
    <template v-if="$slots.title || title" #header>
      <div :class="ns.e('title')">
        <slot v-if="$slots.title" name="title" />
        <template v-else>
          <div>
            <span ref="titleRef" @mouseenter="handleTitleMouseEnter">{{ title }}</span>
            <el-tooltip v-if="titleTip || $slots['title-tip']" effect="light" placement="top" :append-to-body="true" :z-index="9999">
              <template #content>
                <slot v-if="$slots['title-tip']" name="title-tip" />
                <span v-else>{{ titleTip }}</span>
              </template>
              <el-icon :class="ns.e('title-tip')" style="margin-left: 4px; cursor: help">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
      </div>
    </template>

    <!-- 内容 -->
    <slot />

    <!-- 底部插槽 -->
    <template v-if="footerVisible || $slots.footer" #footer>
      <div v-if="$slots.footer">
        <slot name="footer" />
      </div>
      <div v-else :class="{ [ns.m('has-left')]: !!$slots['footer-left'] }">
        <div v-if="$slots['footer-left']" :class="ns.e('footer-left')">
          <slot name="footer-left" />
        </div>
        <div>
          <Button :type="cancelButtonConfig.type" :disabled="cancelButtonConfig.disabled" @click="handleCancel">
            {{ cancelButtonConfig.text }}
          </Button>
          <Button
            :type="confirmButtonConfig.type"
            :disabled="confirmButtonConfig.disabled"
            :loading="confirmLoading ? loading : false"
            @click="handleConfirm"
          >
            {{ confirmButtonConfig.text }}
          </Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { ElIcon, ElTooltip } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { BsDialog as Dialog } from '@bs-nexus/components/element-plus/dialog'
import { BsButton as Button } from '@bs-nexus/components/element-plus/button'
import { DISABLE_ANIMATION_CLASSNAME, useLocale, useNamespace } from '@bs-nexus/hooks'
import { isObject } from '@bs-nexus/utils'
import { type DialogButtonConfig, dialogEmits, dialogProps } from './dialog'

defineOptions({
  name: 'BsDialog'
})

const props = defineProps(dialogProps)
const emit = defineEmits(dialogEmits)

const { t } = useLocale()
const ns = useNamespace('dialog')

// 直接使用 props.disableAnimation 保持响应性

const loading = ref(false)
const titleRef = ref<HTMLElement>()

// 使用 computed 实现双向绑定
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue' as any, value)
})

// 计算对话框宽度
const dialogWidth = computed(() => {
  // 优先使用width
  if (props.width) {
    return typeof props.width === 'number' ? `${props.width}px` : props.width
  }

  if (props.size === 'small' || !props.size) {
    return '480px'
  } else if (props.size === 'default') {
    return '600px'
  } else if (props.size === 'large') {
    return '800px'
  } else if (props.size === 'xlarge') {
    return '1000px'
  } else {
    return '480px'
  }
})

// 计算对话框样式类
const dialogClass = computed(() => {
  return `${ns.b()} ${ns.m(props.size)}`
})

// 重置按钮属性
const resetBtnAttrs = (config: DialogButtonConfig | Record<string, any>, isConfirm = false): DialogButtonConfig => {
  const defaultConfig: DialogButtonConfig = isConfirm
    ? {
        type: 'primary',
        disabled: false,
        text: t('el.messagebox.confirm')
      }
    : {
        type: '',
        disabled: false,
        text: t('el.messagebox.cancel')
      }

  if (isObject(config)) {
    const result = Object.assign(defaultConfig, config)
    return result
  } else {
    return defaultConfig
  }
}

// 确认按钮配置
const confirmButtonConfig = computed(() => {
  return resetBtnAttrs(props.confirmButton, true)
})

// 取消按钮配置
const cancelButtonConfig = computed(() => {
  return resetBtnAttrs(props.cancelButton)
})

// 处理关闭前回调
const handleBeforeClose = (done: () => void) => {
  if (props.beforeClose && typeof props.beforeClose === 'function') {
    props.beforeClose(done)
  } else {
    done()
  }
}

// 处理关闭事件
const handleClose = () => {
  emit('cancel')
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  handleBeforeClose(() => {
    dialogVisible.value = false
  })
}

// 处理确认按钮点击
const handleConfirm = () => {
  if (props.confirmLoading) {
    loading.value = true
    emit('ok', () => {
      loading.value = false
    })
  } else {
    emit('ok')
  }
}

const handleTitleMouseEnter = () => {
  if (titleRef.value) {
    const element = titleRef.value
    const isTextOverflowing = element.scrollWidth > element.clientWidth

    if (isTextOverflowing) {
      element.setAttribute('title', props.title || '')
    } else {
      element.removeAttribute('title')
    }
  }
}
</script>
