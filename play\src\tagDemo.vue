<script lang="ts" setup>
import { ref, computed } from 'vue'
import BsTagGroup from '../../packages/components/tag-group'
import { ElMessage } from 'element-plus'

// 模拟100条数据
const tableData = ref(
  Array.from({ length: 100 }, (_, i) => ({
    id: i + 1,
    date: `2025-07-${String((i % 30) + 1).padStart(2, '0')}`,
    name: `用户${i + 1}`,
    address: `地址${i + 1}`,
    tags: Array.from({ length: Math.floor(Math.random() * 10) + 1 }, (_, j) => ({
      label: `标签${i}${j + 1}`,
      color: ['primary', 'success', 'danger', 'warning', 'info'][j % 5]
    }))
  }))
)

// 分页配置
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => tableData.value.length)

// 当前页数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})
</script>

<template>
  <div class="header-demo">
    <el-table :data="currentPageData" style="width: 100%" border>
      <el-table-column prop="date" label="日期" width="180" />
      <el-table-column prop="name" label="姓名" width="180" />
      <el-table-column prop="address" label="地址" />
      <el-table-column label="标签" width="250">
        <template #default="{ row }">
          <bs-tag-group :options="row.tags" />
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[5, 10, 20, 50]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 20px"
    />
    <bs-tag color="purple" type="dark">xxx</bs-tag>
    <bs-tag color="purple" type="dot">xxx</bs-tag>
    <bs-tag color="danger" type="light-round">xxx</bs-tag>
    <bs-tag color="warning" type="dark">xxx</bs-tag>
    <bs-tag color="info">xxx</bs-tag>
    <bs-tag color="primary" type="plain">xxx</bs-tag>
    <bs-tag color="primary" type="plain-round" closable>xxx</bs-tag>

    <bs-tag size="large" color="primary" type="plain-round" closable> xxx </bs-tag>
    <bs-tag size="medium" color="danger" type="plain-round" closable> xxx </bs-tag>
    <bs-tag size="small" color="success" type="dark" closable> xxx </bs-tag>
  </div>
</template>
