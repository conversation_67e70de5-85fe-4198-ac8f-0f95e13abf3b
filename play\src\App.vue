<template>
  <!-- If you want to apply a custom namespace, uncomment the following and apply the css configuration in `vite.config.ts`. -->
  <!-- <bs-config-provider namespace="ep"> -->
  <div class="play-container">
    <bs-icon color="#409EFC">
      <Check />
    </bs-icon>
    <bs-icon>
      <Menu />
    </bs-icon>
    <bs-button>test</bs-button>
    <bs-affix>affix</bs-affix>
    <div v-loading="true" />
  </div>
  <div style="padding: 10px">
    <vxe-table round :data="tableData" border size="small">
      <vxe-column type="seq" width="70"></vxe-column>
      <vxe-column field="name" title="Name"></vxe-column>
      <vxe-column field="sex" title="Sex"></vxe-column>
      <vxe-column field="age" title="Age"></vxe-column>
    </vxe-table>
  </div>
  <!-- </bs-config-provider> -->
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Check, Menu } from '@bs-nexus/icons-vue'
import { VxeTable, VxeColumn } from '@bs-nexus/components/vxe-table'
interface RowVO {
  id: number
  name: string
  role: string
  sex: string
  age: number
  address: string
}

const tableData = ref<RowVO[]>([
  {
    id: 10001,
    name: 'Test1',
    role: 'Develop',
    sex: 'Man',
    age: 28,
    address: 'test abc'
  },
  { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
  { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
  { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' }
])
</script>

<style lang="scss">
html,
body {
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;

  #play {
    height: 100%;
    width: 100%;

    // .play-container {
    //   height: 100%;
    //   width: 100%;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    // }
  }
}
</style>
