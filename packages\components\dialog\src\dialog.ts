/* eslint-disable @typescript-eslint/no-unused-vars */
import { buildProps, definePropType, isBoolean } from '@bs-nexus/utils'
import { useDisableAnimationProps } from '@bs-nexus/hooks'
import { UPDATE_MODEL_EVENT } from '@bs-nexus/constants'
import type { ExtractPropTypes } from 'vue'
import type { ButtonType } from '@bs-nexus/components/element-plus/button'

/**
 * @description 对话框按钮配置接口
 */
export interface DialogButtonConfig {
  /** 按钮类型 */
  type?: ButtonType
  /** 按钮文本 */
  text?: string
  /** 是否禁用 */
  disabled?: boolean
}

export const dialogProps = buildProps({
  /**
   * @description 是否显示 Dialog
   */
  modelValue: {
    type: Boolean,
    default: false
  },
  /**
   * @description Dialog 对话框 Dialog 的标题
   */
  title: String,
  /**
   * @description 标题提示信息
   */
  titleTip: String,
  /**
   * @description 对话框的尺寸 (xlarge、large、default、small)
   */
  size: {
    type: String,
    default: 'small',
    values: ['xlarge', 'large', 'default', 'small']
  },
  /**
   * @description 对话框的宽度 (不设置默认值，外部传入值为空时根据属性size来设置对应的宽度)
   */
  width: {
    type: [String, Number]
  },
  /**
   * @description 是否为全屏 Dialog
   */
  fullscreen: {
    type: Boolean,
    default: false
  },
  /**
   * @description dialog CSS 中的 margin-top 值 (不设置默认值，默认垂直居中)
   */
  top: {
    type: String
  },
  /**
   * @description 是否需要遮罩层
   */
  modal: {
    type: Boolean,
    default: true
  },
  /**
   * @description header 部分的自定义 class 名 (与内样式类进行组合)
   */
  headerClass: String,
  /**
   * @description body 部分的自定义 class 名
   */
  bodyClass: String,
  /**
   * @description footer 部分的自定义 class 名
   */
  footerClass: String,
  /**
   * @description Dialog 自身是否插入至 body 元素上
   */
  appendToBody: {
    type: Boolean,
    default: false
  },
  /**
   * @description Dialog 挂载到哪个 DOM 元素将覆盖 append-to-body
   */
  appendTo: {
    type: String,
    default: 'body'
  },
  /**
   * @description 是否可以通过点击 modal 关闭 Dialog (默认值改为false)
   */
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  /**
   * @description 是否可以通过按下 ESC 关闭 Dialog (默认值改为false)
   */
  closeOnPressEscape: {
    type: Boolean,
    default: false
  },
  /**
   * @description 关闭前的回调 (优化现有功能，结合确认按钮的loading状态)
   */
  beforeClose: {
    type: definePropType<(done: () => void) => void>(Function)
  },
  /**
   * @description 是否水平垂直对齐对话框 (默认改为true，默认需要居中)
   */
  alignCenter: {
    type: Boolean,
    default: true
  },
  /**
   * @description 当关闭 Dialog 时，销毁其中的元素
   */
  destroyOnClose: {
    type: Boolean,
    default: false
  },
  /**
   * @description 是否显示底部
   */
  footerVisible: {
    type: Boolean,
    default: true
  },
  /**
   * @description 确认按钮配置
   */
  confirmButton: {
    type: definePropType<DialogButtonConfig>(Object),
    default: (): DialogButtonConfig => ({
      type: 'primary'
    })
  },
  /**
   * @description 取消按钮配置
   */
  cancelButton: {
    type: definePropType<DialogButtonConfig>(Object),
    default: (): DialogButtonConfig => ({
      type: ''
    })
  },
  /**
   * @description 确认按钮是否为加载中状态
   */
  confirmLoading: {
    type: Boolean,
    default: false
  },
  ...useDisableAnimationProps
} as const)

export type DialogProps = ExtractPropTypes<typeof dialogProps>

export const dialogEmits = {
  /** 更新 model-value 事件 */
  [UPDATE_MODEL_EVENT]: (value: boolean) => isBoolean(value),
  /** 取消事件 */
  cancel: () => true,
  /** 确认事件 */
  ok: (..._args: any[]) => true
}

export type DialogEmits = typeof dialogEmits
